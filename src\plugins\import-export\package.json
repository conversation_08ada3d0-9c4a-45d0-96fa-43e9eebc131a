{"name": "strapi-plugin-import-export-entries", "version": "1.23.1", "description": "Import/Export data from and to your database in just few clicks.", "strapi": {"name": "import-export-entries", "description": "Import/Export data from and to your database in just few clicks.", "kind": "plugin", "displayName": "Import Export Entries"}, "files": ["strapi-admin.js", "strapi-server.js", "admin/", "doc/", "libs/", "server/"], "scripts": {"build": "tsc --build", "build:clean": "rm -rf libs server types", "dev": "nodemon --exec yarn build", "lint:check": "eslint ./src ./admin && yarn prettier --check ./src ./admin", "lint:fix": "eslint ./src ./admin --fix && yarn prettier --write ./src ./admin", "test": "jest --forceExit --detectOpenHandles --runInBand", "test:watch": "jest --forceExit --detectOpenHandles --runInBand --watch", "prepublishOnly": "yarn build:clean && yarn build", "release": "standard-version", "release:patch": "standard-version --release-as patch", "release:minor": "standard-version --release-as minor", "release:major": "standard-version --release-as major"}, "dependencies": {"@monaco-editor/react": "4.4.5", "csvtojson": "2.0.10", "deepmerge": "^4.2.2", "joi": "17.6.0", "lodash": "4.17.21", "monaco-editor": "0.33.0", "monaco-editor-webpack-plugin": "7.0.1", "node-fetch": "2.6.9", "react-singleton-hook": "3.3.0"}, "devDependencies": {"@babel/core": "^7.18.5", "@babel/eslint-parser": "^7.18.2", "@babel/preset-react": "^7.17.12", "@faker-js/faker": "^7.5.0", "@strapi/plugin-i18n": "^4.10.5", "@strapi/plugin-seo": "^1.8.0", "@strapi/plugin-users-permissions": "^4.10.5", "@strapi/strapi": "^4.10.5", "@types/fs-extra": "^11.0.1", "@types/jest": "^29.5.8", "@types/node-fetch": "^2.6.3", "better-sqlite3": "^9.1.1", "eslint": "^8.23.0", "eslint-plugin-jest": "^27.0.1", "eslint-plugin-react": "^7.30.1", "eslint-plugin-simple-import-sort": "^8.0.0", "jest": "^29.0.2", "nodemon": "^2.0.22", "prettier": "^2.7.1", "standard-version": "^9.5.0", "supertest": "^6.2.4", "ts-jest": "^29.1.1", "typescript": "^5.0.3"}, "peerDependencies": {"@strapi/strapi": "^4.10.5"}, "author": {"name": "Baboo", "url": "https://github.com/Baboo7"}, "maintainers": [{"name": "Baboo", "url": "https://github.com/Baboo7"}], "engines": {"node": ">=14.19.1 <=20.x.x", "npm": ">=6.0.0"}, "keywords": ["strapi", "plugin", "strapi", "import", "data", "export", "data", "content"], "repository": {"type": "git", "url": "git+https://github.com/Baboo7/strapi-plugin-import-export-entries.git"}, "bugs": {"url": "https://github.com/Baboo7/strapi-plugin-import-export-entries/issues"}, "homepage": "https://github.com/Baboo7/strapi-plugin-import-export-entries#readme", "license": "MIT"}