# Private S3 + CloudFront Configuration Summary

## 🔒 Architecture Overview

Your Strapi upload configuration has been updated to use a **secure private S3 bucket** with **CloudFront distribution** for public file access.

### Key Components:
- **S3 Bucket**: `maruti-site-cdn` (private, blocks all public access)
- **Region**: `ap-south-1` (Asia Pacific - Mumbai)
- **CloudFront**: CDN distribution for public file serving
- **Security**: Files accessible only through CloudFront, not direct S3 URLs

## ⚡ Key Changes Made

### 1. Configuration Updates (`config/plugins.ts`)
```typescript
// ❌ REMOVED: Public ACL settings
// ACL: "public-read" - No longer needed

// ✅ ADDED: Private bucket configuration
upload: {
  config: {
    provider: "@strapi/provider-upload-aws-s3",
    providerOptions: {
      s3Options: {
        // No ACL specified - private bucket
        region: 'ap-south-1',
        params: {
          Bucket: "maruti-site-cdn",
        },
      },
      // CloudFront URL for public access
      baseUrl: process.env.CLOUDFRONT_URL,
    },
  },
}
```

### 2. Environment Variables (`.env`)
```env
# AWS S3 Configuration (Private Bucket + CloudFront)
AWS_ACCESS_KEY_ID=your_aws_access_key_id_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key_here
AWS_REGION=ap-south-1
AWS_S3_BUCKET=maruti-site-cdn

# CloudFront Configuration
CLOUDFRONT_URL=https://your-cloudfront-domain.cloudfront.net
S3_ROOT_PATH=
```

## 🛠️ Required AWS Setup

### S3 Bucket Configuration:
- ✅ **Block all public access** enabled
- ✅ **Bucket name**: `maruti-site-cdn`
- ✅ **Region**: `ap-south-1`
- ✅ **CORS**: Configured for upload operations

### CloudFront Distribution:
- ✅ **Origin**: S3 bucket with Origin Access Control (OAC)
- ✅ **Protocol**: HTTPS redirect enabled
- ✅ **Methods**: GET, HEAD, OPTIONS, PUT, POST, PATCH, DELETE
- ✅ **Cache**: Configured for optimal performance

### IAM User Permissions:
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject", 
        "s3:DeleteObject"
      ],
      "Resource": "arn:aws:s3:::maruti-site-cdn/*"
    },
    {
      "Effect": "Allow",
      "Action": ["s3:ListBucket"],
      "Resource": "arn:aws:s3:::maruti-site-cdn"
    }
  ]
}
```

## 🔐 Security Benefits

1. **Private S3 Bucket**: Files cannot be accessed directly via S3 URLs
2. **CloudFront Access Control**: Only CloudFront can access S3 objects
3. **HTTPS Enforcement**: All file access through secure HTTPS
4. **No Public ACL**: Eliminates risk of accidental public exposure
5. **Origin Access Control**: Modern AWS security best practice

## 📋 File URL Structure

### Before (Public S3):
```
https://maruti-site-cdn.s3.ap-south-1.amazonaws.com/filename.jpg
```

### After (CloudFront):
```
https://d1234567890.cloudfront.net/filename.jpg
```

## ✅ Testing Checklist

- [ ] S3 bucket created with public access blocked
- [ ] CloudFront distribution configured with OAC
- [ ] S3 bucket policy updated for CloudFront access
- [ ] Environment variables updated with CloudFront URL
- [ ] Strapi application restarted
- [ ] File upload test successful
- [ ] File URLs use CloudFront domain (not S3)
- [ ] Files accessible through CloudFront URLs
- [ ] Direct S3 URLs return access denied (expected)

## 🚨 Important Notes

1. **File URLs**: All uploaded files will now use CloudFront URLs
2. **Cache Propagation**: New files may take a few minutes to be available globally
3. **Direct S3 Access**: Direct S3 URLs will return "Access Denied" (this is correct)
4. **Cache Invalidation**: May be needed for updated files
5. **Performance**: Global CDN provides faster file delivery

## 🔧 Environment Variable Setup

Replace these placeholders in your `.env` file:

```env
# Get from AWS IAM User
AWS_ACCESS_KEY_ID=AKIA...
AWS_SECRET_ACCESS_KEY=...

# Get from CloudFront Distribution
CLOUDFRONT_URL=https://d1234567890.cloudfront.net

# Optional: Subdirectory in bucket
S3_ROOT_PATH=uploads/
```

## 📞 Support

- **AWS S3 Issues**: Check IAM permissions and bucket policy
- **CloudFront Issues**: Verify OAC configuration and distribution settings
- **File Access Issues**: Ensure CloudFront URL is correct in environment variables
- **Upload Issues**: Check CORS configuration on S3 bucket

This configuration provides enterprise-grade security and performance for your file uploads! 🚀
