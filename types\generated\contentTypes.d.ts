import type { Schema, Attribute } from '@strapi/strapi';

export interface AdminPermission extends Schema.CollectionType {
  collectionName: 'admin_permissions';
  info: {
    name: 'Permission';
    description: '';
    singularName: 'permission';
    pluralName: 'permissions';
    displayName: 'Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    actionParameters: Attribute.JSON & Attribute.DefaultTo<{}>;
    subject: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    properties: Attribute.JSON & Attribute.DefaultTo<{}>;
    conditions: Attribute.JSON & Attribute.DefaultTo<[]>;
    role: Attribute.Relation<'admin::permission', 'manyToOne', 'admin::role'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminUser extends Schema.CollectionType {
  collectionName: 'admin_users';
  info: {
    name: 'User';
    description: '';
    singularName: 'user';
    pluralName: 'users';
    displayName: 'User';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    firstname: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    lastname: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    username: Attribute.String;
    email: Attribute.Email &
      Attribute.Required &
      Attribute.Private &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    password: Attribute.Password &
      Attribute.Private &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    resetPasswordToken: Attribute.String & Attribute.Private;
    registrationToken: Attribute.String & Attribute.Private;
    isActive: Attribute.Boolean &
      Attribute.Private &
      Attribute.DefaultTo<false>;
    roles: Attribute.Relation<'admin::user', 'manyToMany', 'admin::role'> &
      Attribute.Private;
    blocked: Attribute.Boolean & Attribute.Private & Attribute.DefaultTo<false>;
    preferedLanguage: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'admin::user', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'admin::user', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface AdminRole extends Schema.CollectionType {
  collectionName: 'admin_roles';
  info: {
    name: 'Role';
    description: '';
    singularName: 'role';
    pluralName: 'roles';
    displayName: 'Role';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    code: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    description: Attribute.String;
    users: Attribute.Relation<'admin::role', 'manyToMany', 'admin::user'>;
    permissions: Attribute.Relation<
      'admin::role',
      'oneToMany',
      'admin::permission'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'admin::role', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'admin::role', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface AdminApiToken extends Schema.CollectionType {
  collectionName: 'strapi_api_tokens';
  info: {
    name: 'Api Token';
    singularName: 'api-token';
    pluralName: 'api-tokens';
    displayName: 'Api Token';
    description: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    description: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Attribute.DefaultTo<''>;
    type: Attribute.Enumeration<['read-only', 'full-access', 'custom']> &
      Attribute.Required &
      Attribute.DefaultTo<'read-only'>;
    accessKey: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    lastUsedAt: Attribute.DateTime;
    permissions: Attribute.Relation<
      'admin::api-token',
      'oneToMany',
      'admin::api-token-permission'
    >;
    expiresAt: Attribute.DateTime;
    lifespan: Attribute.BigInteger;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::api-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::api-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminApiTokenPermission extends Schema.CollectionType {
  collectionName: 'strapi_api_token_permissions';
  info: {
    name: 'API Token Permission';
    description: '';
    singularName: 'api-token-permission';
    pluralName: 'api-token-permissions';
    displayName: 'API Token Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    token: Attribute.Relation<
      'admin::api-token-permission',
      'manyToOne',
      'admin::api-token'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::api-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::api-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminTransferToken extends Schema.CollectionType {
  collectionName: 'strapi_transfer_tokens';
  info: {
    name: 'Transfer Token';
    singularName: 'transfer-token';
    pluralName: 'transfer-tokens';
    displayName: 'Transfer Token';
    description: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    description: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Attribute.DefaultTo<''>;
    accessKey: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    lastUsedAt: Attribute.DateTime;
    permissions: Attribute.Relation<
      'admin::transfer-token',
      'oneToMany',
      'admin::transfer-token-permission'
    >;
    expiresAt: Attribute.DateTime;
    lifespan: Attribute.BigInteger;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::transfer-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::transfer-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminTransferTokenPermission extends Schema.CollectionType {
  collectionName: 'strapi_transfer_token_permissions';
  info: {
    name: 'Transfer Token Permission';
    description: '';
    singularName: 'transfer-token-permission';
    pluralName: 'transfer-token-permissions';
    displayName: 'Transfer Token Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    token: Attribute.Relation<
      'admin::transfer-token-permission',
      'manyToOne',
      'admin::transfer-token'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::transfer-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::transfer-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUploadFile extends Schema.CollectionType {
  collectionName: 'files';
  info: {
    singularName: 'file';
    pluralName: 'files';
    displayName: 'File';
    description: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    alternativeText: Attribute.String;
    caption: Attribute.String;
    width: Attribute.Integer;
    height: Attribute.Integer;
    formats: Attribute.JSON;
    hash: Attribute.String & Attribute.Required;
    ext: Attribute.String;
    mime: Attribute.String & Attribute.Required;
    size: Attribute.Decimal & Attribute.Required;
    url: Attribute.String & Attribute.Required;
    previewUrl: Attribute.String;
    provider: Attribute.String & Attribute.Required;
    provider_metadata: Attribute.JSON;
    related: Attribute.Relation<'plugin::upload.file', 'morphToMany'>;
    folder: Attribute.Relation<
      'plugin::upload.file',
      'manyToOne',
      'plugin::upload.folder'
    > &
      Attribute.Private;
    folderPath: Attribute.String &
      Attribute.Required &
      Attribute.Private &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::upload.file',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::upload.file',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUploadFolder extends Schema.CollectionType {
  collectionName: 'upload_folders';
  info: {
    singularName: 'folder';
    pluralName: 'folders';
    displayName: 'Folder';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    pathId: Attribute.Integer & Attribute.Required & Attribute.Unique;
    parent: Attribute.Relation<
      'plugin::upload.folder',
      'manyToOne',
      'plugin::upload.folder'
    >;
    children: Attribute.Relation<
      'plugin::upload.folder',
      'oneToMany',
      'plugin::upload.folder'
    >;
    files: Attribute.Relation<
      'plugin::upload.folder',
      'oneToMany',
      'plugin::upload.file'
    >;
    path: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::upload.folder',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::upload.folder',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginContentReleasesRelease extends Schema.CollectionType {
  collectionName: 'strapi_releases';
  info: {
    singularName: 'release';
    pluralName: 'releases';
    displayName: 'Release';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    releasedAt: Attribute.DateTime;
    scheduledAt: Attribute.DateTime;
    timezone: Attribute.String;
    status: Attribute.Enumeration<
      ['ready', 'blocked', 'failed', 'done', 'empty']
    > &
      Attribute.Required;
    actions: Attribute.Relation<
      'plugin::content-releases.release',
      'oneToMany',
      'plugin::content-releases.release-action'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::content-releases.release',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::content-releases.release',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginContentReleasesReleaseAction
  extends Schema.CollectionType {
  collectionName: 'strapi_release_actions';
  info: {
    singularName: 'release-action';
    pluralName: 'release-actions';
    displayName: 'Release Action';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    type: Attribute.Enumeration<['publish', 'unpublish']> & Attribute.Required;
    entry: Attribute.Relation<
      'plugin::content-releases.release-action',
      'morphToOne'
    >;
    contentType: Attribute.String & Attribute.Required;
    locale: Attribute.String;
    release: Attribute.Relation<
      'plugin::content-releases.release-action',
      'manyToOne',
      'plugin::content-releases.release'
    >;
    isEntryValid: Attribute.Boolean;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::content-releases.release-action',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::content-releases.release-action',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginI18NLocale extends Schema.CollectionType {
  collectionName: 'i18n_locale';
  info: {
    singularName: 'locale';
    pluralName: 'locales';
    collectionName: 'locales';
    displayName: 'Locale';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetMinMax<
        {
          min: 1;
          max: 50;
        },
        number
      >;
    code: Attribute.String & Attribute.Unique;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::i18n.locale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::i18n.locale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsPermission
  extends Schema.CollectionType {
  collectionName: 'up_permissions';
  info: {
    name: 'permission';
    description: '';
    singularName: 'permission';
    pluralName: 'permissions';
    displayName: 'Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String & Attribute.Required;
    role: Attribute.Relation<
      'plugin::users-permissions.permission',
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsRole extends Schema.CollectionType {
  collectionName: 'up_roles';
  info: {
    name: 'role';
    description: '';
    singularName: 'role';
    pluralName: 'roles';
    displayName: 'Role';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    description: Attribute.String;
    type: Attribute.String & Attribute.Unique;
    permissions: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToMany',
      'plugin::users-permissions.permission'
    >;
    users: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToMany',
      'plugin::users-permissions.user'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsUser extends Schema.CollectionType {
  collectionName: 'up_users';
  info: {
    name: 'user';
    description: '';
    singularName: 'user';
    pluralName: 'users';
    displayName: 'User';
  };
  options: {
    draftAndPublish: false;
    timestamps: true;
  };
  attributes: {
    username: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    email: Attribute.Email &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    provider: Attribute.String;
    password: Attribute.Password &
      Attribute.Private &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    resetPasswordToken: Attribute.String & Attribute.Private;
    confirmationToken: Attribute.String & Attribute.Private;
    confirmed: Attribute.Boolean & Attribute.DefaultTo<false>;
    blocked: Attribute.Boolean & Attribute.DefaultTo<false>;
    role: Attribute.Relation<
      'plugin::users-permissions.user',
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiAboutUsAboutUs extends Schema.SingleType {
  collectionName: 'about_uses';
  info: {
    singularName: 'about-us';
    pluralName: 'about-uses';
    displayName: 'About Us';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    rich_text: Attribute.Component<'rich-text.rich-text'>;
    our_story: Attribute.Component<'service-delivery-process.service-delivery-process'>;
    hero_section: Attribute.Component<'hero-section.about-us-hero-section'>;
    vision_mission: Attribute.Component<'vision-mission.vision-mission', true>;
    pr_and_news: Attribute.Component<'pr-and-news.pr-and-news'>;
    download_our_brand: Attribute.Component<'cta.cta'>;
    meet_our_people: Attribute.Component<'events-pages.our-people'>;
    seo: Attribute.Component<'seo.seo'>;
    featured_on: Attribute.Component<'trusted-partner.trusted-partner'>;
    services_delivery_process: Attribute.Component<'common.title-image'>;
    build_careers: Attribute.Component<'common.title-image-repeat'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::about-us.about-us',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::about-us.about-us',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiAiReadinessAiReadiness extends Schema.SingleType {
  collectionName: 'ai_readinesses';
  info: {
    singularName: 'ai-readiness';
    pluralName: 'ai-readinesses';
    displayName: 'AI Readiness';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    hero_section: Attribute.Component<'common.title-description-image-button'>;
    ai_readiness_components: Attribute.Relation<
      'api::ai-readiness.ai-readiness',
      'oneToMany',
      'api::ai-readiness-component.ai-readiness-component'
    >;
    form: Attribute.Component<'form.form'>;
    restart_button: Attribute.Component<'common.button'>;
    consultation_button: Attribute.Component<'common.button'>;
    tag_list: Attribute.Component<'ai-readiness.options', true>;
    tag: Attribute.Component<'common.title-description'>;
    score_heading: Attribute.String;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::ai-readiness.ai-readiness',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::ai-readiness.ai-readiness',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiAiReadinessComponentAiReadinessComponent
  extends Schema.CollectionType {
  collectionName: 'ai_readiness_components';
  info: {
    singularName: 'ai-readiness-component';
    pluralName: 'ai-readiness-components';
    displayName: 'AI Readiness Component';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    heading: Attribute.String;
    section_weight: Attribute.Decimal;
    question: Attribute.Component<'ai-readiness.question', true>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::ai-readiness-component.ai-readiness-component',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::ai-readiness-component.ai-readiness-component',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiAllResourcesPageAllResourcesPage extends Schema.SingleType {
  collectionName: 'all_resources_pages';
  info: {
    singularName: 'all-resources-page';
    pluralName: 'all-resources-pages';
    displayName: 'All resources page';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    page_name: Attribute.String;
    hero_section: Attribute.Component<'common.title-image'>;
    resources_silder: Attribute.Component<
      'resources-page-slider.resources-page-slider',
      true
    >;
    filter_ui: Attribute.Component<'resources-filter.resources-filter'>;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::all-resources-page.all-resources-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::all-resources-page.all-resources-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiAllServicePageAllServicePage extends Schema.SingleType {
  collectionName: 'all_service_pages';
  info: {
    singularName: 'all-service-page';
    pluralName: 'all-service-pages';
    displayName: 'all_service_page';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    richtext_with_title: Attribute.Component<'rich-text.richtext-with-title'>;
    all_service_page: Attribute.Component<
      'all-service-page.all-service-page',
      true
    >;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::all-service-page.all-service-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::all-service-page.all-service-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiAuthorAuthor extends Schema.CollectionType {
  collectionName: 'authors';
  info: {
    singularName: 'author';
    pluralName: 'authors';
    displayName: 'author';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String;
    designation: Attribute.String;
    image: Attribute.Media;
    description: Attribute.RichText;
    slug: Attribute.String;
    blogs: Attribute.Relation<
      'api::author.author',
      'manyToMany',
      'api::blog.blog'
    >;
    linkedin_link: Attribute.String;
    twitter_link: Attribute.String;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::author.author',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::author.author',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiAwardAward extends Schema.SingleType {
  collectionName: 'awards';
  info: {
    singularName: 'award';
    pluralName: 'awards';
    displayName: 'Award';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    awards: Attribute.Component<'recognition-awards.recognitions-awards'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::award.award',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::award.award',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiBlogBlog extends Schema.CollectionType {
  collectionName: 'blogs';
  info: {
    singularName: 'blog';
    pluralName: 'blogs';
    displayName: 'blogs';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    description: Attribute.Text & Attribute.Required;
    type: Attribute.Enumeration<
      [
        'Agile',
        'Artificial Intelligence and Machine Learning',
        'Block Chain',
        'Bot Development',
        'Business Strategy',
        'Chatbot',
        'Cloud',
        'Data Analytics and Business Intelligence',
        'Devops',
        'Low Code No Code Development',
        'Product Development',
        'QA',
        'Robotic Process Automation',
        'Salesforce Development',
        'Software Development Practices',
        'Achievements',
        'User Experience'
      ]
    > &
      Attribute.Required;
    content: Attribute.Component<'blog.content', true>;
    suggestions: Attribute.Component<'blog.suggestions'>;
    caseStudy_suggestions: Attribute.Component<'blog.case-study-suggestions'>;
    slug: Attribute.String & Attribute.Required;
    image: Attribute.Media & Attribute.Required;
    seo: Attribute.Component<'seo.seo'>;
    heroSection_image: Attribute.Media & Attribute.Required;
    authors: Attribute.Relation<
      'api::blog.blog',
      'manyToMany',
      'api::author.author'
    >;
    global_services: Attribute.Relation<
      'api::blog.blog',
      'oneToMany',
      'api::global-service.global-service'
    >;
    global_industries: Attribute.Relation<
      'api::blog.blog',
      'oneToMany',
      'api::global-industry.global-industry'
    >;
    blog_related_service: Attribute.Component<'blog.blog-related-service'>;
    audio_file: Attribute.Media;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::blog.blog', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'api::blog.blog', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiBlogListingPageBlogListingPage extends Schema.SingleType {
  collectionName: 'blog_listing_pages';
  info: {
    singularName: 'blog-listing-page';
    pluralName: 'blog-listing-pages';
    displayName: 'Blog Listing Page';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    hero_section: Attribute.Component<'case-study.hero-section'>;
    filter: Attribute.Component<'case-study.filters'>;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::blog-listing-page.blog-listing-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::blog-listing-page.blog-listing-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCareerCareer extends Schema.SingleType {
  collectionName: 'careers';
  info: {
    singularName: 'career';
    pluralName: 'careers';
    displayName: 'Career';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    hero_section: Attribute.Component<'rich-text.rich-text'>;
    gptw: Attribute.Component<'common.title-description-image'>;
    life_at_mtl: Attribute.Component<'careers.life-at-mtl'>;
    core_values: Attribute.Component<'careers.core-values'>;
    employee_testimonial: Attribute.Component<'employee-testimonial.employee-testimonial'>;
    benefits: Attribute.Component<'benefits.benefits'>;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::career.career',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::career.career',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCaseStudyCaseStudy extends Schema.CollectionType {
  collectionName: 'case_studies';
  info: {
    singularName: 'case-study';
    pluralName: 'case-studies';
    displayName: 'Case Studies';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    hero_section: Attribute.Component<'case-studies.hero-section'>;
    title: Attribute.String;
    preview: Attribute.Component<'case-studies.preview'>;
    slug: Attribute.String & Attribute.Required;
    richText_for_TheClient: Attribute.Component<'rich-text.richtext-with-title'>;
    richText_for_Challenges: Attribute.Component<'rich-text.richtext-with-title'>;
    richText_for_Solutions: Attribute.Component<'rich-text.richtext-with-title'>;
    richText_for_Results: Attribute.Component<'rich-text.richtext-with-title'>;
    quote: Attribute.Component<'case-studies.quote'>;
    caseStudy_form: Attribute.Component<'form.case-study-form'>;
    global_resource_type: Attribute.Relation<
      'api::case-study.case-study',
      'oneToOne',
      'api::global-resource-type.global-resource-type'
    >;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::case-study.case-study',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::case-study.case-study',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCaseStudyListingPageCaseStudyListingPage
  extends Schema.SingleType {
  collectionName: 'case_study_listing_pages';
  info: {
    singularName: 'case-study-listing-page';
    pluralName: 'case-study-listing-pages';
    displayName: 'Case Study Listing Page';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    hero_section: Attribute.Component<'case-study.hero-section'>;
    filter: Attribute.Component<'case-study.filters'>;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::case-study-listing-page.case-study-listing-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::case-study-listing-page.case-study-listing-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCityServicePageCityServicePage
  extends Schema.CollectionType {
  collectionName: 'city_service_pages';
  info: {
    singularName: 'city-service-page';
    pluralName: 'city-service-pages';
    displayName: 'CityServicePage';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    whyChooseMtl: Attribute.Component<'why-choose-mtl.why-choose-mtl'>;
    page_name: Attribute.String;
    slug: Attribute.String & Attribute.Required;
    hero_section: Attribute.Component<'common.title-description-image'>;
    cta: Attribute.Component<'cta.cta'>;
    service_offering_card: Attribute.Component<'l2-services.l2-services'>;
    insights: Attribute.Component<'insights.insights'>;
    other_services: Attribute.Component<'other-services.l3-other-services'>;
    tech_stack: Attribute.Component<'tech-stack.tech-stack'>;
    our_service_delivery_process: Attribute.Component<'service-delivery-process.service-delivery-process'>;
    case_study_cards: Attribute.Component<'case-study.case-study-relation'>;
    cta_2: Attribute.Component<'cta.cta'>;
    seo: Attribute.Component<'seo.seo'>;
    challenges: Attribute.Component<'audit-methodology.audit-methodology'>;
    audit_review: Attribute.Component<'common.title-description'>;
    our_audit_methodology: Attribute.Component<'audit-methodology.audit-methodology'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::city-service-page.city-service-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::city-service-page.city-service-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCloudConsultingCloudConsulting
  extends Schema.CollectionType {
  collectionName: 'cloud_consultings';
  info: {
    singularName: 'cloud-consulting';
    pluralName: 'cloud-consultings';
    displayName: 'Cloud Consulting';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    cta: Attribute.Component<'cta.cta'>;
    pageName: Attribute.String;
    slug: Attribute.String & Attribute.Required;
    our_audit_methodology: Attribute.Component<'audit-methodology.audit-methodology'>;
    deliverables: Attribute.Component<'audit-methodology.audit-methodology'>;
    seo: Attribute.Component<'seo.seo'>;
    hero_section: Attribute.Component<'common.title-desc-image-link'>;
    challenges: Attribute.Component<'audit-methodology.audit-methodology'>;
    audit_button: Attribute.Component<'common.button'>;
    solution_with_video: Attribute.Component<'podcast-page.podcast-series'>;
    solution: Attribute.Component<'common.title-description'>;
    why_choose_mtl: Attribute.Component<'why-choose-mtl.why-choose-mtl'>;
    faq: Attribute.Component<'faq.faq'>;
    scope_and_deliverables: Attribute.Component<'audit-methodology.audit-methodology'>;
    tech_stack: Attribute.Component<'tech-stack.tech-stack'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::cloud-consulting.cloud-consulting',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::cloud-consulting.cloud-consulting',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCloudMigrationCalculatorCloudMigrationCalculator
  extends Schema.SingleType {
  collectionName: 'cloud_migration_calculators';
  info: {
    singularName: 'cloud-migration-calculator';
    pluralName: 'cloud-migration-calculators';
    displayName: 'Cloud Migration Calculator';
    description: 'Cloud Migration Cost Calculator assessment tool';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    hero_section: Attribute.Component<'common.title-description-image-button'>;
    cloud_migration_components: Attribute.Relation<
      'api::cloud-migration-calculator.cloud-migration-calculator',
      'oneToMany',
      'api::cloud-migration-component.cloud-migration-component'
    >;
    form: Attribute.Component<'form.form'>;
    restart_button: Attribute.Component<'common.button'>;
    consultation_button: Attribute.Component<'common.button'>;
    cost_calculation: Attribute.Component<'cloud-migration.cost-calculation'>;
    result_heading: Attribute.String;
    result_description: Attribute.RichText;
    cost_breakdown_title: Attribute.String;
    disclaimer: Attribute.RichText;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::cloud-migration-calculator.cloud-migration-calculator',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::cloud-migration-calculator.cloud-migration-calculator',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCloudMigrationComponentCloudMigrationComponent
  extends Schema.CollectionType {
  collectionName: 'cloud_migration_components';
  info: {
    singularName: 'cloud-migration-component';
    pluralName: 'cloud-migration-components';
    displayName: 'Cloud Migration Component';
    description: 'Individual sections/components of the cloud migration assessment';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    heading: Attribute.String;
    section_number: Attribute.Integer;
    description: Attribute.RichText;
    questions: Attribute.Component<'cloud-migration.question', true>;
    cost_weight: Attribute.Decimal & Attribute.DefaultTo<1>;
    is_required_section: Attribute.Boolean & Attribute.DefaultTo<true>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::cloud-migration-component.cloud-migration-component',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::cloud-migration-component.cloud-migration-component',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiContactUsContactUs extends Schema.SingleType {
  collectionName: 'contact_uses';
  info: {
    singularName: 'contact-us';
    pluralName: 'contact-uses';
    displayName: 'contact_us';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    left_section_content: Attribute.Component<'contact-us-form.contact-us-form'>;
    right_section_content: Attribute.Component<'contact-us-form.right-side-content'>;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::contact-us.contact-us',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::contact-us.contact-us',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCookiePolicyCookiePolicy extends Schema.SingleType {
  collectionName: 'cookie_policies';
  info: {
    singularName: 'cookie-policy';
    pluralName: 'cookie-policies';
    displayName: 'Cookie-policy';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    rich_text: Attribute.RichText;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::cookie-policy.cookie-policy',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::cookie-policy.cookie-policy',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiEBookEBook extends Schema.CollectionType {
  collectionName: 'e_books';
  info: {
    singularName: 'e-book';
    pluralName: 'e-books';
    displayName: 'eBook';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    hero_section: Attribute.Component<'case-study.hero-section'>;
    rich_text: Attribute.RichText;
    form: Attribute.Component<'form.case-study-form'>;
    title: Attribute.String;
    slug: Attribute.String & Attribute.Required;
    global_resource_type: Attribute.Relation<
      'api::e-book.e-book',
      'oneToOne',
      'api::global-resource-type.global-resource-type'
    >;
    preview: Attribute.Component<'ebooks.preview'>;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::e-book.e-book',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::e-book.e-book',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiEBooksListingPageEBooksListingPage
  extends Schema.SingleType {
  collectionName: 'e_books_listing_pages';
  info: {
    singularName: 'e-books-listing-page';
    pluralName: 'e-books-listing-pages';
    displayName: 'eBooks Listing Page';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    hero_section: Attribute.Component<'case-study.hero-section'>;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::e-books-listing-page.e-books-listing-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::e-books-listing-page.e-books-listing-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiEventListingPageEventListingPage extends Schema.SingleType {
  collectionName: 'event_listing_pages';
  info: {
    singularName: 'event-listing-page';
    pluralName: 'event-listing-pages';
    displayName: 'Event Listing Page';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    hero_section: Attribute.Component<'common.title-description-image'>;
    form: Attribute.Component<'form.form'>;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::event-listing-page.event-listing-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::event-listing-page.event-listing-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiEventMainPageEventMainPage extends Schema.CollectionType {
  collectionName: 'event_main_pages';
  info: {
    singularName: 'event-main-page';
    pluralName: 'event-main-pages';
    displayName: 'Event Main Page';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    why_choose_mtl: Attribute.Component<'why-choose-mtl.why-choose-mtl'>;
    hero_section: Attribute.Component<'events-pages.hero-section'>;
    about_event: Attribute.Component<'rich-text.richtext-with-title'>;
    offerings: Attribute.Component<'events-pages.offerings-card'>;
    meet_our_people: Attribute.Component<'events-pages.our-people'>;
    slug: Attribute.String & Attribute.Required;
    page_name: Attribute.String;
    global_resource_type: Attribute.Relation<
      'api::event-main-page.event-main-page',
      'oneToOne',
      'api::global-resource-type.global-resource-type'
    >;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::event-main-page.event-main-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::event-main-page.event-main-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiFooterFooter extends Schema.SingleType {
  collectionName: 'footers';
  info: {
    singularName: 'footer';
    pluralName: 'footers';
    displayName: 'Footer';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    terms_and_condition_section: Attribute.Component<'footer.third-row', true>;
    company_logo_section: Attribute.Component<'footer.fourth-row'>;
    sector_row: Attribute.Component<'common.link-box', true>;
    pages_row: Attribute.Component<'common.link-box', true>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::footer.footer',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::footer.footer',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiFormForm extends Schema.SingleType {
  collectionName: 'forms';
  info: {
    singularName: 'form';
    pluralName: 'forms';
    displayName: 'Form';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    form: Attribute.Component<'form.form'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::form.form', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'api::form.form', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiGlobalIndustryGlobalIndustry extends Schema.CollectionType {
  collectionName: 'global_industries';
  info: {
    singularName: 'global-industry';
    pluralName: 'global-industries';
    displayName: 'Global Industry';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    industry_title: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::global-industry.global-industry',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::global-industry.global-industry',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiGlobalResourceTypeGlobalResourceType
  extends Schema.CollectionType {
  collectionName: 'global_resource_types';
  info: {
    singularName: 'global-resource-type';
    pluralName: 'global-resource-types';
    displayName: 'Global Resource Type';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    resource_type_title: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::global-resource-type.global-resource-type',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::global-resource-type.global-resource-type',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiGlobalServiceGlobalService extends Schema.CollectionType {
  collectionName: 'global_services';
  info: {
    singularName: 'global-service';
    pluralName: 'global-services';
    displayName: 'Global Service';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    service_title: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::global-service.global-service',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::global-service.global-service',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiGlobalVideoTagGlobalVideoTag extends Schema.CollectionType {
  collectionName: 'global_video_tags';
  info: {
    singularName: 'global-video-tag';
    pluralName: 'global-video-tags';
    displayName: 'Global Video Tag';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    tag: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::global-video-tag.global-video-tag',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::global-video-tag.global-video-tag',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiHeaderHeader extends Schema.SingleType {
  collectionName: 'headers';
  info: {
    singularName: 'header';
    pluralName: 'headers';
    displayName: 'Header';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    logo: Attribute.Component<'header.logo'>;
    menu: Attribute.DynamicZone<
      [
        'header.menu-1',
        'header.menu-2',
        'header.menu-3',
        'header.menu-4',
        'header.menu-5'
      ]
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::header.header',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::header.header',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiHomePageHomePage extends Schema.SingleType {
  collectionName: 'home_pages';
  info: {
    singularName: 'home-page';
    pluralName: 'home-pages';
    displayName: 'HomePage';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    hero_section: Attribute.Component<'hero-section.home-hero-section', true>;
    Company_Statistics: Attribute.Component<'company-statistics.company-statistics'>;
    Industries: Attribute.Component<'industries-card.industries-card'>;
    our_services: Attribute.Component<'our-services.our-services'>;
    insights: Attribute.Component<'insights.insights'>;
    case_study_cards: Attribute.Component<'case-study.case-study-relation'>;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::home-page.home-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::home-page.home-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiIndustryIndustry extends Schema.CollectionType {
  collectionName: 'industries';
  info: {
    singularName: 'industry';
    pluralName: 'industries';
    displayName: 'Industry';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    CTA: Attribute.Component<'cta.cta'>;
    CTA2: Attribute.Component<'cta.cta'>;
    what_service_we_are_offering: Attribute.Component<'l2-services.l2-services'>;
    why_choose_maruti_techlabs: Attribute.Component<'why-choose-mtl.why-choose-mtl'>;
    industry_awards: Attribute.Component<'recognition-awards.recognitions-awards'>;
    clutch_reviews: Attribute.Component<'common.clutch-reviews'>;
    insights: Attribute.Component<'insights.insights'>;
    faq: Attribute.Component<'faq.faq'>;
    form: Attribute.Component<'form.form'>;
    slug: Attribute.String & Attribute.Required;
    pageName: Attribute.String;
    hero_section: Attribute.Component<'common.title-description-image'>;
    case_study_cards: Attribute.Component<'case-study.case-study-relation'>;
    tech_stack: Attribute.Component<'tech-stack.tech-stack'>;
    challenges_and_solutions: Attribute.Component<'tab-challenges.tab-challenges'>;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::industry.industry',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::industry.industry',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiL2ServicePageL2ServicePage extends Schema.CollectionType {
  collectionName: 'l2_service_pages';
  info: {
    singularName: 'l2-service-page';
    pluralName: 'l2-service-pages';
    displayName: 'L2ServicePages';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    cta: Attribute.Component<'cta.cta'>;
    pageName: Attribute.String;
    slug: Attribute.String & Attribute.Required;
    hero_section: Attribute.Component<'common.title-description-image'>;
    other_services: Attribute.Component<'other-services.other-services'>;
    faq: Attribute.Component<'faq.faq'>;
    ServiceOfferingCard: Attribute.Component<'l2-services.l2-services'>;
    insights: Attribute.Component<'insights.insights'>;
    cta_other: Attribute.Component<'cta.cta'>;
    case_study_cards: Attribute.Component<'case-study.case-study-relation'>;
    l_3_service_pages: Attribute.Relation<
      'api::l2-service-page.l2-service-page',
      'oneToMany',
      'api::l3-service-page.l3-service-page'
    >;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::l2-service-page.l2-service-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::l2-service-page.l2-service-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiL3ServicePageL3ServicePage extends Schema.CollectionType {
  collectionName: 'l3_services_pages';
  info: {
    singularName: 'l3-service-page';
    pluralName: 'l3-services-pages';
    displayName: 'L3ServicePages';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    pageName: Attribute.String;
    slug: Attribute.String & Attribute.Required;
    whyChooseMtl: Attribute.Component<'why-choose-mtl.why-choose-mtl'>;
    hero_section: Attribute.Component<'common.title-description-image'>;
    cta: Attribute.Component<'cta.cta'>;
    service_offering_card: Attribute.Component<'l2-services.l2-services'>;
    insights: Attribute.Component<'insights.insights'>;
    other_services: Attribute.Component<'other-services.l3-other-services'>;
    tech_stack: Attribute.Component<'tech-stack.tech-stack'>;
    our_service_delivery_process: Attribute.Component<'service-delivery-process.service-delivery-process'>;
    case_study_cards: Attribute.Component<'case-study.case-study-relation'>;
    l_2_service_page: Attribute.Relation<
      'api::l3-service-page.l3-service-page',
      'manyToOne',
      'api::l2-service-page.l2-service-page'
    >;
    cta_2: Attribute.Component<'cta.cta'>;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::l3-service-page.l3-service-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::l3-service-page.l3-service-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiNewNew extends Schema.CollectionType {
  collectionName: 'news';
  info: {
    singularName: 'new';
    pluralName: 'news';
    displayName: 'News';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    title: Attribute.String;
    image: Attribute.Media;
    slug: Attribute.String & Attribute.Required;
    date: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::new.new', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'api::new.new', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiPartnerPartner extends Schema.CollectionType {
  collectionName: 'partners';
  info: {
    singularName: 'partner';
    pluralName: 'partners';
    displayName: 'Partner';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Industries: Attribute.Component<'industries-card.industries-card'>;
    cta: Attribute.Component<'cta.cta'>;
    meet_our_people: Attribute.Component<'events-pages.our-people'>;
    cta_other: Attribute.Component<'cta.cta'>;
    case_study_cards: Attribute.Component<'case-study.case-study-relation'>;
    insights: Attribute.Component<'insights.insights'>;
    hero_section: Attribute.Component<'common.title-desc-image-link'>;
    page_name: Attribute.String;
    slug: Attribute.String & Attribute.Required;
    service_offering: Attribute.Component<'l2-services.l2-services'>;
    seo: Attribute.Component<'seo.seo'>;
    news_and_events: Attribute.Component<'partners.news-events'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::partner.partner',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::partner.partner',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiPodcastPagePodcastPage extends Schema.SingleType {
  collectionName: 'podcast_pages';
  info: {
    singularName: 'podcast-page';
    pluralName: 'podcast-pages';
    displayName: 'Podcast Page';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    hero_section: Attribute.Component<'podcast-page.hero-section'>;
    listen_on: Attribute.Component<'podcast-page.listen-on'>;
    podcast_series: Attribute.Component<'podcast-page.podcast-series', true>;
    latest_episode: Attribute.Component<'podcast-page.latest-episode'>;
    play_button_across_page: Attribute.Media;
    CTA: Attribute.Component<'cta.cta'>;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::podcast-page.podcast-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::podcast-page.podcast-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiPrPr extends Schema.CollectionType {
  collectionName: 'prs';
  info: {
    singularName: 'pr';
    pluralName: 'prs';
    displayName: 'PR';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    title: Attribute.String;
    image: Attribute.Media;
    slug: Attribute.String & Attribute.Required;
    date: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::pr.pr', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'api::pr.pr', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiPrivacyPolicyPrivacyPolicy extends Schema.SingleType {
  collectionName: 'privacy_policies';
  info: {
    singularName: 'privacy-policy';
    pluralName: 'privacy-policies';
    displayName: 'Privacy-policy';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    rich_text: Attribute.RichText;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::privacy-policy.privacy-policy',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::privacy-policy.privacy-policy',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiRetailComponentRetailComponent
  extends Schema.CollectionType {
  collectionName: 'retail_components';
  info: {
    singularName: 'retail-component';
    pluralName: 'retail-components';
    displayName: 'RetailComponent';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    challenges_and_solutions: Attribute.Component<'audit-methodology.audit-methodology'>;
    what_service_we_are_offering: Attribute.Component<'l2-services.l2-services'>;
    cta: Attribute.Component<'cta.cta'>;
    case_study_cards: Attribute.Component<'case-study.case-study-relation'>;
    why_choose_maruti_techlabs: Attribute.Component<'why-choose-mtl.why-choose-mtl'>;
    cta_other: Attribute.Component<'cta.cta'>;
    tech_stack: Attribute.Component<'tech-stack.tech-stack'>;
    clutch_reviews: Attribute.Component<'common.clutch-reviews'>;
    insights: Attribute.Component<'insights.insights'>;
    faq: Attribute.Component<'faq.faq'>;
    tab_title: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::retail-component.retail-component',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::retail-component.retail-component',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiRetailPageRetailPage extends Schema.CollectionType {
  collectionName: 'retail_pages';
  info: {
    singularName: 'retail-page';
    pluralName: 'retail-pages';
    displayName: 'RetailPage';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    hero_section: Attribute.Component<'common.title-description-image'>;
    pageName: Attribute.String;
    slug: Attribute.String & Attribute.Required;
    tab_section: Attribute.Component<'common.title-description'>;
    retail_components: Attribute.Relation<
      'api::retail-page.retail-page',
      'oneToMany',
      'api::retail-component.retail-component'
    >;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::retail-page.retail-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::retail-page.retail-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSearchSearch extends Schema.SingleType {
  collectionName: 'searches';
  info: {
    singularName: 'search';
    pluralName: 'searches';
    displayName: 'Search';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    search_input_title: Attribute.String;
    search_input_placeholder: Attribute.String;
    all_results_title: Attribute.String;
    initial_search_results: Attribute.Component<'search.initial-search-results'> &
      Attribute.Required;
    showing_top_20_results_title: Attribute.RichText;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::search.search',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::search.search',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSolutionSolution extends Schema.CollectionType {
  collectionName: 'solutions';
  info: {
    singularName: 'solution';
    pluralName: 'solutions';
    displayName: 'Solution';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    hero_section: Attribute.Component<'common.title-description-image'>;
    cta: Attribute.Component<'cta.cta'>;
    case_study_cards: Attribute.Component<'case-study.case-study-relation'>;
    tech_stack: Attribute.Component<'tech-stack.tech-stack'>;
    why_choose_mtl: Attribute.Component<'why-choose-mtl.why-choose-mtl'>;
    page_name: Attribute.String;
    slug: Attribute.String & Attribute.Required;
    solution: Attribute.Component<'common.title-description'>;
    challenges: Attribute.Component<'solution-page.challenges'>;
    business_use_cases: Attribute.Component<'solution-page.challenges'>;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::solution.solution',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::solution.solution',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTestTest extends Schema.CollectionType {
  collectionName: 'tests';
  info: {
    singularName: 'test';
    pluralName: 'tests';
    displayName: 'test';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.RichText;
    image: Attribute.Media;
    kuldip: Attribute.RichText;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::test.test', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'api::test.test', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiTestimonialTestimonial extends Schema.SingleType {
  collectionName: 'testimonials';
  info: {
    singularName: 'testimonial';
    pluralName: 'testimonials';
    displayName: 'testimonial';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    testimonials: Attribute.Component<'testimonials.testimonials'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::testimonial.testimonial',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::testimonial.testimonial',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiThankYouThankYou extends Schema.SingleType {
  collectionName: 'thank_yous';
  info: {
    singularName: 'thank-you';
    pluralName: 'thank-yous';
    displayName: 'Thank You';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    thank_you: Attribute.Component<'thank-you.thank-you'>;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::thank-you.thank-you',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::thank-you.thank-you',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTrustedPartnerTrustedPartner extends Schema.SingleType {
  collectionName: 'trusted_partners';
  info: {
    singularName: 'trusted-partner';
    pluralName: 'trusted-partners';
    displayName: 'TrustedPartner';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    trustedPartner: Attribute.Component<'trusted-partner.trusted-partner'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::trusted-partner.trusted-partner',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::trusted-partner.trusted-partner',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiVideoVideo extends Schema.CollectionType {
  collectionName: 'videos';
  info: {
    singularName: 'video';
    pluralName: 'videos';
    displayName: 'video';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    url: Attribute.String;
    publication_date: Attribute.Date;
    title: Attribute.String;
    thumbnail: Attribute.Media;
    global_video_tag: Attribute.Relation<
      'api::video.video',
      'oneToOne',
      'api::global-video-tag.global-video-tag'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::video.video',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::video.video',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiVideoPageVideoPage extends Schema.SingleType {
  collectionName: 'video_pages';
  info: {
    singularName: 'video-page';
    pluralName: 'video-pages';
    displayName: 'video_page';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    hero_section: Attribute.Component<'common.title-image'>;
    all_videos: Attribute.Component<'video-page.all-videos'>;
    play_button_across_page: Attribute.Media;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::video-page.video-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::video-page.video-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiWhitePaperWhitePaper extends Schema.CollectionType {
  collectionName: 'white_papers';
  info: {
    singularName: 'white-paper';
    pluralName: 'white-papers';
    displayName: 'White Paper';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    hero_section: Attribute.Component<'case-study.hero-section'>;
    rich_text: Attribute.RichText;
    title: Attribute.String;
    slug: Attribute.String & Attribute.Required;
    global_resource_type: Attribute.Relation<
      'api::white-paper.white-paper',
      'oneToOne',
      'api::global-resource-type.global-resource-type'
    >;
    preview: Attribute.Component<'ebooks.preview'>;
    seo: Attribute.Component<'seo.seo'>;
    form: Attribute.Component<'form.case-study-form'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::white-paper.white-paper',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::white-paper.white-paper',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiWhitePapersListingPageWhitePapersListingPage
  extends Schema.SingleType {
  collectionName: 'white_papers_listing_pages';
  info: {
    singularName: 'white-papers-listing-page';
    pluralName: 'white-papers-listing-pages';
    displayName: 'white-papers listing page';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    hero_section: Attribute.Component<'case-study.hero-section'>;
    seo: Attribute.Component<'seo.seo'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::white-papers-listing-page.white-papers-listing-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::white-papers-listing-page.white-papers-listing-page',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

declare module '@strapi/types' {
  export module Shared {
    export interface ContentTypes {
      'admin::permission': AdminPermission;
      'admin::user': AdminUser;
      'admin::role': AdminRole;
      'admin::api-token': AdminApiToken;
      'admin::api-token-permission': AdminApiTokenPermission;
      'admin::transfer-token': AdminTransferToken;
      'admin::transfer-token-permission': AdminTransferTokenPermission;
      'plugin::upload.file': PluginUploadFile;
      'plugin::upload.folder': PluginUploadFolder;
      'plugin::content-releases.release': PluginContentReleasesRelease;
      'plugin::content-releases.release-action': PluginContentReleasesReleaseAction;
      'plugin::i18n.locale': PluginI18NLocale;
      'plugin::users-permissions.permission': PluginUsersPermissionsPermission;
      'plugin::users-permissions.role': PluginUsersPermissionsRole;
      'plugin::users-permissions.user': PluginUsersPermissionsUser;
      'api::about-us.about-us': ApiAboutUsAboutUs;
      'api::ai-readiness.ai-readiness': ApiAiReadinessAiReadiness;
      'api::ai-readiness-component.ai-readiness-component': ApiAiReadinessComponentAiReadinessComponent;
      'api::all-resources-page.all-resources-page': ApiAllResourcesPageAllResourcesPage;
      'api::all-service-page.all-service-page': ApiAllServicePageAllServicePage;
      'api::author.author': ApiAuthorAuthor;
      'api::award.award': ApiAwardAward;
      'api::blog.blog': ApiBlogBlog;
      'api::blog-listing-page.blog-listing-page': ApiBlogListingPageBlogListingPage;
      'api::career.career': ApiCareerCareer;
      'api::case-study.case-study': ApiCaseStudyCaseStudy;
      'api::case-study-listing-page.case-study-listing-page': ApiCaseStudyListingPageCaseStudyListingPage;
      'api::city-service-page.city-service-page': ApiCityServicePageCityServicePage;
      'api::cloud-consulting.cloud-consulting': ApiCloudConsultingCloudConsulting;
      'api::cloud-migration-calculator.cloud-migration-calculator': ApiCloudMigrationCalculatorCloudMigrationCalculator;
      'api::cloud-migration-component.cloud-migration-component': ApiCloudMigrationComponentCloudMigrationComponent;
      'api::contact-us.contact-us': ApiContactUsContactUs;
      'api::cookie-policy.cookie-policy': ApiCookiePolicyCookiePolicy;
      'api::e-book.e-book': ApiEBookEBook;
      'api::e-books-listing-page.e-books-listing-page': ApiEBooksListingPageEBooksListingPage;
      'api::event-listing-page.event-listing-page': ApiEventListingPageEventListingPage;
      'api::event-main-page.event-main-page': ApiEventMainPageEventMainPage;
      'api::footer.footer': ApiFooterFooter;
      'api::form.form': ApiFormForm;
      'api::global-industry.global-industry': ApiGlobalIndustryGlobalIndustry;
      'api::global-resource-type.global-resource-type': ApiGlobalResourceTypeGlobalResourceType;
      'api::global-service.global-service': ApiGlobalServiceGlobalService;
      'api::global-video-tag.global-video-tag': ApiGlobalVideoTagGlobalVideoTag;
      'api::header.header': ApiHeaderHeader;
      'api::home-page.home-page': ApiHomePageHomePage;
      'api::industry.industry': ApiIndustryIndustry;
      'api::l2-service-page.l2-service-page': ApiL2ServicePageL2ServicePage;
      'api::l3-service-page.l3-service-page': ApiL3ServicePageL3ServicePage;
      'api::new.new': ApiNewNew;
      'api::partner.partner': ApiPartnerPartner;
      'api::podcast-page.podcast-page': ApiPodcastPagePodcastPage;
      'api::pr.pr': ApiPrPr;
      'api::privacy-policy.privacy-policy': ApiPrivacyPolicyPrivacyPolicy;
      'api::retail-component.retail-component': ApiRetailComponentRetailComponent;
      'api::retail-page.retail-page': ApiRetailPageRetailPage;
      'api::search.search': ApiSearchSearch;
      'api::solution.solution': ApiSolutionSolution;
      'api::test.test': ApiTestTest;
      'api::testimonial.testimonial': ApiTestimonialTestimonial;
      'api::thank-you.thank-you': ApiThankYouThankYou;
      'api::trusted-partner.trusted-partner': ApiTrustedPartnerTrustedPartner;
      'api::video.video': ApiVideoVideo;
      'api::video-page.video-page': ApiVideoPageVideoPage;
      'api::white-paper.white-paper': ApiWhitePaperWhitePaper;
      'api::white-papers-listing-page.white-papers-listing-page': ApiWhitePapersListingPageWhitePapersListingPage;
    }
  }
}
