{"kind": "singleType", "collectionName": "headers", "info": {"singularName": "header", "pluralName": "headers", "displayName": "Header", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"logo": {"type": "component", "repeatable": false, "component": "header.logo"}, "menu": {"type": "dynamiczone", "components": ["header.menu-1", "header.menu-2", "header.menu-3", "header.menu-4", "header.menu-5"]}}}