{"kind": "singleType", "collectionName": "home_pages", "info": {"singularName": "home-page", "pluralName": "home-pages", "displayName": "HomePage", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"hero_section": {"type": "component", "repeatable": true, "component": "hero-section.home-hero-section"}, "Company_Statistics": {"type": "component", "repeatable": false, "component": "company-statistics.company-statistics"}, "Industries": {"type": "component", "repeatable": false, "component": "industries-card.industries-card"}, "our_services": {"type": "component", "repeatable": false, "component": "our-services.our-services"}, "insights": {"type": "component", "repeatable": false, "component": "insights.insights"}, "case_study_cards": {"type": "component", "repeatable": false, "component": "case-study.case-study-relation"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}}}