{"kind": "collectionType", "collectionName": "videos", "info": {"singularName": "video", "pluralName": "videos", "displayName": "video", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"url": {"type": "string"}, "publication_date": {"type": "date"}, "title": {"type": "string"}, "thumbnail": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "global_video_tag": {"type": "relation", "relation": "oneToOne", "target": "api::global-video-tag.global-video-tag"}}}