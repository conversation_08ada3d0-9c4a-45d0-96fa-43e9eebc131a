{"kind": "singleType", "collectionName": "blog_listing_pages", "info": {"singularName": "blog-listing-page", "pluralName": "blog-listing-pages", "displayName": "Blog Listing Page", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"hero_section": {"type": "component", "repeatable": false, "component": "case-study.hero-section"}, "filter": {"type": "component", "repeatable": false, "component": "case-study.filters"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}}}