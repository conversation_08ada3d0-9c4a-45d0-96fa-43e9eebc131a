{"kind": "singleType", "collectionName": "all_resources_pages", "info": {"singularName": "all-resources-page", "pluralName": "all-resources-pages", "displayName": "All resources page", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"page_name": {"type": "string"}, "hero_section": {"type": "component", "repeatable": false, "component": "common.title-image"}, "resources_silder": {"type": "component", "repeatable": true, "component": "resources-page-slider.resources-page-slider"}, "filter_ui": {"type": "component", "repeatable": false, "component": "resources-filter.resources-filter"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}}}