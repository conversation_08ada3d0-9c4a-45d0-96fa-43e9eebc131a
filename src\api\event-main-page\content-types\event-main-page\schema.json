{"kind": "collectionType", "collectionName": "event_main_pages", "info": {"singularName": "event-main-page", "pluralName": "event-main-pages", "displayName": "Event Main Page", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"why_choose_mtl": {"type": "component", "repeatable": false, "component": "why-choose-mtl.why-choose-mtl"}, "hero_section": {"type": "component", "repeatable": false, "component": "events-pages.hero-section"}, "about_event": {"type": "component", "repeatable": false, "component": "rich-text.richtext-with-title"}, "offerings": {"type": "component", "repeatable": false, "component": "events-pages.offerings-card"}, "meet_our_people": {"type": "component", "repeatable": false, "component": "events-pages.our-people"}, "slug": {"type": "string", "required": true}, "page_name": {"type": "string"}, "global_resource_type": {"type": "relation", "relation": "oneToOne", "target": "api::global-resource-type.global-resource-type"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}}}