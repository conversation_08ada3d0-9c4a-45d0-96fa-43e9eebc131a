{"collectionName": "components_all_service_page_all_service_pages", "info": {"displayName": "AllServicePage"}, "options": {}, "attributes": {"tag": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "richtext"}, "image": {"allowedTypes": ["images", "files", "videos", "audios"], "type": "media", "multiple": false}, "button": {"type": "component", "repeatable": false, "component": "common.button"}, "l_2_service_pages": {"type": "relation", "relation": "oneToMany", "target": "api::l2-service-page.l2-service-page"}}}