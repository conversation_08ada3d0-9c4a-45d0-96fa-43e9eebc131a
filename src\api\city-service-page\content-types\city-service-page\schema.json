{"kind": "collectionType", "collectionName": "city_service_pages", "info": {"singularName": "city-service-page", "pluralName": "city-service-pages", "displayName": "CityServicePage", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"whyChooseMtl": {"type": "component", "repeatable": false, "component": "why-choose-mtl.why-choose-mtl"}, "page_name": {"type": "string"}, "slug": {"type": "string", "required": true}, "hero_section": {"type": "component", "repeatable": false, "component": "common.title-description-image"}, "cta": {"type": "component", "repeatable": false, "component": "cta.cta"}, "service_offering_card": {"type": "component", "repeatable": false, "component": "l2-services.l2-services"}, "insights": {"type": "component", "repeatable": false, "component": "insights.insights"}, "other_services": {"type": "component", "repeatable": false, "component": "other-services.l3-other-services"}, "tech_stack": {"type": "component", "repeatable": false, "component": "tech-stack.tech-stack"}, "our_service_delivery_process": {"type": "component", "repeatable": false, "component": "service-delivery-process.service-delivery-process"}, "case_study_cards": {"type": "component", "repeatable": false, "component": "case-study.case-study-relation"}, "cta_2": {"type": "component", "repeatable": false, "component": "cta.cta"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}, "challenges": {"type": "component", "repeatable": false, "component": "audit-methodology.audit-methodology"}, "audit_review": {"type": "component", "repeatable": false, "component": "common.title-description"}, "our_audit_methodology": {"type": "component", "repeatable": false, "component": "audit-methodology.audit-methodology"}}}