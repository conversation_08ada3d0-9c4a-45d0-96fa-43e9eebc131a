{"kind": "singleType", "collectionName": "event_listing_pages", "info": {"singularName": "event-listing-page", "pluralName": "event-listing-pages", "displayName": "Event Listing Page", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"hero_section": {"type": "component", "repeatable": false, "component": "common.title-description-image"}, "form": {"type": "component", "repeatable": false, "component": "form.form"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}}}