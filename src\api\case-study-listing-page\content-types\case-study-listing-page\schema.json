{"kind": "singleType", "collectionName": "case_study_listing_pages", "info": {"singularName": "case-study-listing-page", "pluralName": "case-study-listing-pages", "displayName": "Case Study Listing Page", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"hero_section": {"displayName": "hero_section", "type": "component", "repeatable": false, "component": "case-study.hero-section"}, "filter": {"displayName": "filters", "type": "component", "repeatable": false, "component": "case-study.filters"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}}}