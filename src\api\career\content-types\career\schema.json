{"kind": "singleType", "collectionName": "careers", "info": {"singularName": "career", "pluralName": "careers", "displayName": "Career", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"hero_section": {"type": "component", "repeatable": false, "component": "rich-text.rich-text"}, "gptw": {"type": "component", "repeatable": false, "component": "common.title-description-image"}, "life_at_mtl": {"displayName": "life_at_mtl", "type": "component", "repeatable": false, "component": "careers.life-at-mtl"}, "core_values": {"displayName": "core_values", "type": "component", "repeatable": false, "component": "careers.core-values"}, "employee_testimonial": {"type": "component", "repeatable": false, "component": "employee-testimonial.employee-testimonial"}, "benefits": {"type": "component", "repeatable": false, "component": "benefits.benefits"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}}}