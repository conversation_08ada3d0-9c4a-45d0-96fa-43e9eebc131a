{"kind": "collectionType", "collectionName": "case_studies", "info": {"singularName": "case-study", "pluralName": "case-studies", "displayName": "Case Studies", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"hero_section": {"displayName": "hero_section", "type": "component", "repeatable": false, "component": "case-studies.hero-section"}, "title": {"type": "string"}, "preview": {"displayName": "preview", "type": "component", "repeatable": false, "component": "case-studies.preview"}, "slug": {"type": "string", "required": true}, "richText_for_TheClient": {"type": "component", "repeatable": false, "component": "rich-text.richtext-with-title"}, "richText_for_Challenges": {"type": "component", "repeatable": false, "component": "rich-text.richtext-with-title"}, "richText_for_Solutions": {"type": "component", "repeatable": false, "component": "rich-text.richtext-with-title"}, "richText_for_Results": {"type": "component", "repeatable": false, "component": "rich-text.richtext-with-title"}, "quote": {"displayName": "quote", "type": "component", "repeatable": false, "component": "case-studies.quote"}, "caseStudy_form": {"displayName": "caseStudy_form", "type": "component", "repeatable": false, "component": "form.case-study-form"}, "global_resource_type": {"type": "relation", "relation": "oneToOne", "target": "api::global-resource-type.global-resource-type"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}}}