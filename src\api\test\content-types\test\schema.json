{"kind": "collectionType", "collectionName": "tests", "info": {"singularName": "test", "pluralName": "tests", "displayName": "test", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": false}, "description": {"type": "richtext"}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "kuldip": {"type": "richtext"}}}