{"collectionName": "components_insights_insights", "info": {"displayName": "Insights", "description": ""}, "options": {}, "attributes": {"title": {"type": "string"}, "subtitle": {"type": "richtext"}, "taglineUrl": {"type": "string"}, "circular_text_image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "blogs": {"type": "relation", "relation": "oneToMany", "target": "api::blog.blog"}}}