{"kind": "collectionType", "collectionName": "news", "info": {"singularName": "new", "pluralName": "news", "displayName": "News", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "slug": {"type": "string", "required": true, "unique": false}, "date": {"type": "string"}}}