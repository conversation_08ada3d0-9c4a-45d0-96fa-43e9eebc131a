{"kind": "collectionType", "collectionName": "l3_services_pages", "info": {"singularName": "l3-service-page", "pluralName": "l3-services-pages", "displayName": "L3ServicePages", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"pageName": {"type": "string"}, "slug": {"type": "string", "required": true}, "whyChooseMtl": {"type": "component", "repeatable": false, "component": "why-choose-mtl.why-choose-mtl"}, "hero_section": {"type": "component", "repeatable": false, "component": "common.title-description-image"}, "cta": {"type": "component", "repeatable": false, "component": "cta.cta"}, "service_offering_card": {"type": "component", "repeatable": false, "component": "l2-services.l2-services"}, "insights": {"type": "component", "repeatable": false, "component": "insights.insights"}, "other_services": {"type": "component", "repeatable": false, "component": "other-services.l3-other-services"}, "tech_stack": {"type": "component", "repeatable": false, "component": "tech-stack.tech-stack"}, "our_service_delivery_process": {"type": "component", "repeatable": false, "component": "service-delivery-process.service-delivery-process"}, "case_study_cards": {"type": "component", "repeatable": false, "component": "case-study.case-study-relation"}, "l_2_service_page": {"type": "relation", "relation": "manyToOne", "target": "api::l2-service-page.l2-service-page", "inversedBy": "l_3_service_pages"}, "cta_2": {"type": "component", "repeatable": false, "component": "cta.cta"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}}}