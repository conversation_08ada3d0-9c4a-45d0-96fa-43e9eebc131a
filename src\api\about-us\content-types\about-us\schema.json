{"kind": "singleType", "collectionName": "about_uses", "info": {"singularName": "about-us", "pluralName": "about-uses", "displayName": "About Us", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"rich_text": {"type": "component", "repeatable": false, "component": "rich-text.rich-text"}, "our_story": {"type": "component", "repeatable": false, "component": "service-delivery-process.service-delivery-process"}, "hero_section": {"type": "component", "repeatable": false, "component": "hero-section.about-us-hero-section"}, "vision_mission": {"type": "component", "repeatable": true, "component": "vision-mission.vision-mission"}, "pr_and_news": {"displayName": "pr_and_news", "type": "component", "repeatable": false, "component": "pr-and-news.pr-and-news"}, "download_our_brand": {"type": "component", "repeatable": false, "component": "cta.cta"}, "meet_our_people": {"type": "component", "repeatable": false, "component": "events-pages.our-people"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}, "featured_on": {"type": "component", "repeatable": false, "component": "trusted-partner.trusted-partner"}, "services_delivery_process": {"type": "component", "repeatable": false, "component": "common.title-image"}, "build_careers": {"displayName": "titleImageRepeat", "type": "component", "repeatable": false, "component": "common.title-image-repeat"}}}