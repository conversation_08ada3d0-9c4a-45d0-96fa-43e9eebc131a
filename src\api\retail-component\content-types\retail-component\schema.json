{"kind": "collectionType", "collectionName": "retail_components", "info": {"singularName": "retail-component", "pluralName": "retail-components", "displayName": "RetailComponent", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"challenges_and_solutions": {"type": "component", "repeatable": false, "component": "audit-methodology.audit-methodology"}, "what_service_we_are_offering": {"type": "component", "repeatable": false, "component": "l2-services.l2-services"}, "cta": {"type": "component", "repeatable": false, "component": "cta.cta"}, "case_study_cards": {"type": "component", "repeatable": false, "component": "case-study.case-study-relation"}, "why_choose_maruti_techlabs": {"type": "component", "repeatable": false, "component": "why-choose-mtl.why-choose-mtl"}, "cta_other": {"type": "component", "repeatable": false, "component": "cta.cta"}, "tech_stack": {"type": "component", "repeatable": false, "component": "tech-stack.tech-stack"}, "clutch_reviews": {"type": "component", "repeatable": false, "component": "common.clutch-reviews"}, "insights": {"type": "component", "repeatable": false, "component": "insights.insights"}, "faq": {"type": "component", "repeatable": false, "component": "faq.faq"}, "tab_title": {"type": "string"}}}