# Cloud Migration Cost Calculator - Strapi Schema Documentation

## Overview

This document outlines the Strapi schema structure created for the "Cloud Migration Cost Calculator" application, based on the requirements from the Google Sheets document and following the patterns established by the existing AI Readiness schema.

## Requirements Analysis

Based on the Google Sheets document, the calculator includes:

### Sections Identified:
1. **Business & Infrastructure Assessment** (Questions 1-7)
2. **Workload & Resource Analysis** (Questions 8-10)
3. **Cloud Provider & Deployment Preferences** (Questions 11-13)
4. **Security, Compliance & Migration Strategy** (Questions 14-15)
5. **Post-Migration & Optimization** (Questions 16-17)
6. **Final Cost Calculation** (Question 18)

### Cost Calculation Logic:
- Fixed costs for specific choices (e.g., server counts, data capacity)
- Percentage-based additions (e.g., +20% for high availability)
- Range-based pricing (e.g., different costs for different server counts)
- Final range calculation (upper range = lower range * 1.3)

## Schema Architecture

### Reused Components from AI Readiness

The following existing components are reused to maintain consistency:

1. **`common.title-description-image-button`** - Hero section
2. **`common.button`** - Action buttons (restart, consultation)
3. **`form.form`** - Contact form integration
4. **`seo.seo`** - SEO metadata

### New Components Created

#### 1. `cloud-migration.options`
**File:** `src/components/cloud-migration/options.json`

```json
{
  "name": "string",           // Option display name
  "value": "decimal",         // Cost value associated with option
  "description": "text"       // Additional description
}
```

**Purpose:** Stores answer options with associated cost values for questions.

#### 2. `cloud-migration.question`
**File:** `src/components/cloud-migration/question.json`

```json
{
  "name": "string",                    // Question text
  "answers": "cloud-migration.options[]", // Available answer options
  "type": "enum",                      // single-choice, multiple-choice, range-based
  "number": "integer",                 // Question number
  "sub_question": "cloud-migration.options[]", // Sub-questions if applicable
  "cost_calculation_type": "enum",     // fixed, percentage, range-based
  "is_required": "boolean"             // Whether question is mandatory
}
```

**Purpose:** Defines individual assessment questions with cost calculation metadata.

#### 3. `cloud-migration.cost-range`
**File:** `src/components/cloud-migration/cost-range.json`

```json
{
  "range_name": "string",     // Range identifier
  "min_value": "decimal",     // Minimum value in range
  "max_value": "decimal",     // Maximum value in range
  "cost": "decimal",          // Associated cost
  "description": "text",      // Range description
  "unit": "string"           // Unit of measurement
}
```

**Purpose:** Handles range-based pricing (e.g., server counts, data capacity ranges).

#### 4. `cloud-migration.pricing-model`
**File:** `src/components/cloud-migration/pricing-model.json`

```json
{
  "model_name": "string",         // Pricing model name
  "base_cost": "decimal",         // Base cost amount
  "percentage_modifier": "decimal", // Percentage modifier for calculations
  "description": "text",          // Model description
  "applicable_to": "enum"         // infrastructure, data, applications, security, compliance, all
}
```

**Purpose:** Defines different pricing models and modifiers for various migration aspects.

#### 5. `cloud-migration.cost-calculation`
**File:** `src/components/cloud-migration/cost-calculation.json`

```json
{
  "calculation_name": "string",           // Calculation identifier
  "base_cost": "decimal",                 // Base calculation cost
  "upper_range_multiplier": "decimal",    // Multiplier for upper range (default: 1.3)
  "formula_description": "text",          // Description of calculation formula
  "cost_components": "cloud-migration.pricing-model[]" // Associated pricing models
}
```

**Purpose:** Configures the overall cost calculation logic and formula.

## Content Types

### 1. Cloud Migration Calculator (Single Type)
**File:** `src/api/cloud-migration-calculator/content-types/cloud-migration-calculator/schema.json`

**Key Attributes:**
- `hero_section`: Hero section with title, description, image, and CTA button
- `cloud_migration_components`: Relation to assessment sections
- `form`: Contact form for lead generation
- `restart_button` & `consultation_button`: Action buttons
- `cost_calculation`: Cost calculation configuration
- `result_heading` & `result_description`: Result display content
- `cost_breakdown_title`: Title for cost breakdown section
- `disclaimer`: Legal disclaimer text
- `seo`: SEO metadata

### 2. Cloud Migration Component (Collection Type)
**File:** `src/api/cloud-migration-component/content-types/cloud-migration-component/schema.json`

**Key Attributes:**
- `heading`: Section title
- `section_number`: Ordering number
- `description`: Section description
- `questions`: Array of assessment questions
- `cost_weight`: Weight factor for cost calculations
- `is_required_section`: Whether section is mandatory

## API Infrastructure

### Generated Files:
1. **Routes:**
   - `src/api/cloud-migration-calculator/routes/cloud-migration-calculator.ts`
   - `src/api/cloud-migration-component/routes/cloud-migration-component.ts`

2. **Controllers:**
   - `src/api/cloud-migration-calculator/controllers/cloud-migration-calculator.ts`
   - `src/api/cloud-migration-component/controllers/cloud-migration-component.ts`

3. **Services:**
   - `src/api/cloud-migration-calculator/services/cloud-migration-calculator.ts`
   - `src/api/cloud-migration-component/services/cloud-migration-component.ts`

## Mapping to Google Sheets Requirements

### Section 1: Business & Infrastructure Assessment
- **Question 1:** "Which elements are you planning to migrate?" → Multiple choice question
- **Question 2:** "How many servers?" → Range-based question with cost tiers
- **Question 3:** "Type of data migration?" → Single choice
- **Question 4:** "Current IT infrastructure setup?" → Single choice
- **Question 5:** "Total server capacity?" → Range-based with cost tiers
- **Question 6:** "Current monthly infrastructure cost?" → Range selection
- **Question 7:** "Main purpose for migration?" → Multiple choice

### Section 2: Workload & Resource Analysis
- **Question 8:** "Type of workloads?" → Multiple choice
- **Question 9:** "Average CPU and memory usage?" → Single choice
- **Question 10:** "High availability/disaster recovery?" → Boolean with cost modifier (+20%)

### Section 3: Cloud Provider & Deployment Preferences
- **Question 11:** "Which cloud provider?" → Single choice
- **Question 12:** "Pricing models?" → Multiple choice
- **Question 13:** "Cloud environments?" → Multiple choice with individual costs

### Section 4: Security, Compliance & Migration Strategy
- **Question 14:** "Compliance requirements?" → Multiple choice with individual costs
- **Question 15:** "Migration strategy?" → Single choice with cost tiers

### Section 5: Post-Migration & Optimization
- **Question 16:** "Auto-scaling capabilities?" → Boolean with fixed cost
- **Question 17:** "Review frequency?" → Single choice (informational)

### Final Calculation
- **Question 18:** Displays calculated cost range (lower and upper bounds)

## Assumptions Made

1. **Cost Storage:** All costs are stored as decimal values to handle fractional amounts
2. **Currency:** No specific currency field - assumes USD based on spreadsheet
3. **Calculation Logic:** Upper range is consistently 30% higher than lower range
4. **Question Types:** Extended beyond AI Readiness to include range-based questions
5. **Multiple Choice Handling:** Multiple selections can accumulate costs additively
6. **Required Fields:** Most questions are required by default for accurate calculations
7. **Percentage Modifiers:** Some questions apply percentage increases to base calculations
8. **Section Weighting:** Each section can have a weight factor for complex calculations

## Integration Points

### Frontend Integration
- Questions can be rendered dynamically based on type (single-choice, multiple-choice, range-based)
- Cost calculations can be performed client-side or server-side
- Results display supports both individual cost breakdown and total ranges

### Data Flow
1. User answers questions → Frontend collects responses
2. Responses mapped to cost values → Calculation engine processes
3. Final cost range calculated → Results displayed to user
4. Optional form submission → Lead capture integration

## Future Enhancements

1. **Dynamic Pricing:** Support for time-based or market-based pricing updates
2. **Regional Costs:** Different cost structures for different geographical regions
3. **Custom Formulas:** More complex calculation formulas beyond simple addition/percentage
4. **Comparison Tools:** Side-by-side comparison of different migration scenarios
5. **Export Functionality:** PDF or Excel export of cost estimates
6. **Integration APIs:** Connect with actual cloud provider pricing APIs

## Strapi Best Practices Followed

1. **Component Reuse:** Leveraged existing common components where possible
2. **Naming Conventions:** Followed kebab-case naming for consistency
3. **Relationship Patterns:** Used oneToMany relations similar to AI Readiness
4. **Content Structure:** Separated configuration (single type) from data (collection type)
5. **SEO Integration:** Included SEO component for search optimization
6. **Form Integration:** Reused existing form components for lead capture
7. **Media Support:** Included image fields for visual content
8. **Rich Text:** Used richtext fields for formatted content display
