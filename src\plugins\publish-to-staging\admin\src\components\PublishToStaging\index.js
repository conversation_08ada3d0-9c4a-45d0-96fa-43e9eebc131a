// ./src/plugins/publish-to-staging/admin/src/components/PublishToStaging/index.js
import React, { useState } from "react";
import { useLocation } from "react-router-dom";
import { useCMEditViewDataManager } from "@strapi/helper-plugin";
import { LinkButton } from "@strapi/design-system/LinkButton";
import CloudUpload from "@strapi/icons/CloudUpload";

const PublishToStaging = () => {
  const [isBuildTriggerButtonDisabled, setisBuildTriggerButtonDisabled] =
    useState(false);

  const triggerBuild = async () => {
    try {
      setisBuildTriggerButtonDisabled(true);
      const response = await fetch(
        "https://api.github.com/repos/Maruti-Techlabs/mtl-nextjs-site/actions/workflows/google-cloudrun-docker.yml/dispatches",
        {
          method: "POST",
          headers: {
            Accept: "application/vnd.github+json",
            Authorization: `Bearer ****************************************`,
            "X-GitHub-Api-Version": "2022-11-28",
            "Content-Type": "application/x-www-form-urlencoded",
          },
          body: '{"ref":"development"}',
        }
      );
      setTimeout(() => {
        setisBuildTriggerButtonDisabled(false);
      }, 10000);
      if (response.ok) {
        window.alert("Staging build started successfully.");
      } else {
        window.alert("Error while triggering Staging build. 1", error);
      }
    } catch (error) {
      setisBuildTriggerButtonDisabled(false);
      window.alert("Error while triggering Staging build. 2", error);
    }
  };

  return (
    <LinkButton
      disabled={isBuildTriggerButtonDisabled}
      size="S"
      startIcon={<CloudUpload />}
      style={{ width: "100%", cursor: "pointer" }}
      variant="secondary"
      //   target="_blank"
      rel="noopener noreferrer"
      onClick={triggerBuild}
      title="page preview"
    >
      Publish to staging
    </LinkButton>
  );
};

export default PublishToStaging;
