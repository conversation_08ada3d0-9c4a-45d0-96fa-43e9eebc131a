{"kind": "collectionType", "collectionName": "e_books", "info": {"singularName": "e-book", "pluralName": "e-books", "displayName": "eBook", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"hero_section": {"type": "component", "repeatable": false, "component": "case-study.hero-section"}, "rich_text": {"type": "richtext"}, "form": {"type": "component", "repeatable": false, "component": "form.case-study-form"}, "title": {"type": "string"}, "slug": {"type": "string", "required": true}, "global_resource_type": {"type": "relation", "relation": "oneToOne", "target": "api::global-resource-type.global-resource-type"}, "preview": {"displayName": "preview", "type": "component", "repeatable": false, "component": "ebooks.preview"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}}}