{"kind": "collectionType", "collectionName": "authors", "info": {"singularName": "author", "pluralName": "authors", "displayName": "author", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "designation": {"type": "string"}, "image": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "description": {"type": "richtext"}, "slug": {"type": "string"}, "blogs": {"type": "relation", "relation": "manyToMany", "target": "api::blog.blog", "inversedBy": "authors"}, "linkedin_link": {"type": "string"}, "twitter_link": {"type": "string"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}}}