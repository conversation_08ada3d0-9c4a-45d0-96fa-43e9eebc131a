{"kind": "collectionType", "collectionName": "retail_pages", "info": {"singularName": "retail-page", "pluralName": "retail-pages", "displayName": "RetailPage", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"hero_section": {"type": "component", "repeatable": false, "component": "common.title-description-image"}, "pageName": {"type": "string"}, "slug": {"type": "string", "required": true}, "tab_section": {"type": "component", "repeatable": false, "component": "common.title-description"}, "retail_components": {"type": "relation", "relation": "oneToMany", "target": "api::retail-component.retail-component"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}}}