{"collectionName": "components_common_blog_heroes", "info": {"displayName": "blog_hero", "description": ""}, "options": {}, "attributes": {"title": {"type": "string"}, "blog_tag": {"type": "string"}, "description": {"type": "richtext"}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "global_services": {"type": "relation", "relation": "oneToMany", "target": "api::global-service.global-service"}, "global_industries": {"type": "relation", "relation": "oneToMany", "target": "api::global-industry.global-industry"}}}