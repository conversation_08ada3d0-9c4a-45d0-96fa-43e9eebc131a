{"kind": "collectionType", "collectionName": "industries", "info": {"singularName": "industry", "pluralName": "industries", "displayName": "Industry", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"CTA": {"type": "component", "repeatable": false, "component": "cta.cta"}, "CTA2": {"type": "component", "repeatable": false, "component": "cta.cta"}, "what_service_we_are_offering": {"type": "component", "repeatable": false, "component": "l2-services.l2-services"}, "why_choose_maruti_techlabs": {"type": "component", "repeatable": false, "component": "why-choose-mtl.why-choose-mtl"}, "industry_awards": {"type": "component", "repeatable": false, "component": "recognition-awards.recognitions-awards"}, "clutch_reviews": {"displayName": "clutch_reviews", "type": "component", "repeatable": false, "component": "common.clutch-reviews"}, "insights": {"type": "component", "repeatable": false, "component": "insights.insights"}, "faq": {"type": "component", "repeatable": false, "component": "faq.faq"}, "form": {"type": "component", "repeatable": false, "component": "form.form"}, "slug": {"type": "string", "required": true}, "pageName": {"type": "string"}, "hero_section": {"type": "component", "repeatable": false, "component": "common.title-description-image"}, "case_study_cards": {"type": "component", "repeatable": false, "component": "case-study.case-study-relation"}, "tech_stack": {"type": "component", "repeatable": false, "component": "tech-stack.tech-stack"}, "challenges_and_solutions": {"type": "component", "repeatable": false, "component": "tab-challenges.tab-challenges"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}}}