{"kind": "collectionType", "collectionName": "solutions", "info": {"singularName": "solution", "pluralName": "solutions", "displayName": "Solution", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"hero_section": {"type": "component", "repeatable": false, "component": "common.title-description-image"}, "cta": {"type": "component", "repeatable": false, "component": "cta.cta"}, "case_study_cards": {"type": "component", "repeatable": false, "component": "case-study.case-study-relation"}, "tech_stack": {"type": "component", "repeatable": false, "component": "tech-stack.tech-stack"}, "why_choose_mtl": {"type": "component", "repeatable": false, "component": "why-choose-mtl.why-choose-mtl"}, "page_name": {"type": "string"}, "slug": {"type": "string", "required": true}, "solution": {"type": "component", "repeatable": false, "component": "common.title-description"}, "challenges": {"type": "component", "repeatable": false, "component": "solution-page.challenges"}, "business_use_cases": {"type": "component", "repeatable": false, "component": "solution-page.challenges"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}}}