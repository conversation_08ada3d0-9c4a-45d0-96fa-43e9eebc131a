{"kind": "collectionType", "collectionName": "l2_service_pages", "info": {"singularName": "l2-service-page", "pluralName": "l2-service-pages", "displayName": "L2ServicePages", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"cta": {"type": "component", "repeatable": false, "component": "cta.cta"}, "pageName": {"type": "string"}, "slug": {"type": "string", "required": true}, "hero_section": {"type": "component", "repeatable": false, "component": "common.title-description-image"}, "other_services": {"type": "component", "repeatable": false, "component": "other-services.other-services"}, "faq": {"type": "component", "repeatable": false, "component": "faq.faq"}, "ServiceOfferingCard": {"type": "component", "repeatable": false, "component": "l2-services.l2-services"}, "insights": {"type": "component", "repeatable": false, "component": "insights.insights"}, "cta_other": {"type": "component", "repeatable": false, "component": "cta.cta"}, "case_study_cards": {"type": "component", "repeatable": false, "component": "case-study.case-study-relation"}, "l_3_service_pages": {"type": "relation", "relation": "oneToMany", "target": "api::l3-service-page.l3-service-page", "mappedBy": "l_2_service_page"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}}}