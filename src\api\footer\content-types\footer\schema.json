{"kind": "singleType", "collectionName": "footers", "info": {"singularName": "footer", "pluralName": "footers", "displayName": "Footer", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"terms_and_condition_section": {"type": "component", "repeatable": true, "component": "footer.third-row"}, "company_logo_section": {"type": "component", "repeatable": false, "component": "footer.fourth-row"}, "sector_row": {"type": "component", "repeatable": true, "component": "common.link-box"}, "pages_row": {"type": "component", "repeatable": true, "component": "common.link-box"}}}