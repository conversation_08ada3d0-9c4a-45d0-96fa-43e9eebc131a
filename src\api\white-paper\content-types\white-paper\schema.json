{"kind": "collectionType", "collectionName": "white_papers", "info": {"singularName": "white-paper", "pluralName": "white-papers", "displayName": "White Paper", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"hero_section": {"type": "component", "repeatable": false, "component": "case-study.hero-section"}, "rich_text": {"type": "richtext"}, "title": {"type": "string"}, "slug": {"type": "string", "required": true}, "global_resource_type": {"type": "relation", "relation": "oneToOne", "target": "api::global-resource-type.global-resource-type"}, "preview": {"type": "component", "repeatable": false, "component": "ebooks.preview"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}, "form": {"type": "component", "repeatable": false, "component": "form.case-study-form"}}}