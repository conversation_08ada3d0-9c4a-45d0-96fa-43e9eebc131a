{"kind": "collectionType", "collectionName": "partners", "info": {"singularName": "partner", "pluralName": "partners", "displayName": "Partner", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"Industries": {"type": "component", "repeatable": false, "component": "industries-card.industries-card"}, "cta": {"type": "component", "repeatable": false, "component": "cta.cta"}, "meet_our_people": {"type": "component", "repeatable": false, "component": "events-pages.our-people"}, "cta_other": {"type": "component", "repeatable": false, "component": "cta.cta"}, "case_study_cards": {"type": "component", "repeatable": false, "component": "case-study.case-study-relation"}, "insights": {"type": "component", "repeatable": false, "component": "insights.insights"}, "hero_section": {"type": "component", "repeatable": false, "component": "common.title-desc-image-link"}, "page_name": {"type": "string"}, "slug": {"type": "string", "required": true}, "service_offering": {"type": "component", "repeatable": false, "component": "l2-services.l2-services"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}, "news_and_events": {"type": "component", "repeatable": false, "component": "partners.news-events"}}}