{"kind": "singleType", "collectionName": "white_papers_listing_pages", "info": {"singularName": "white-papers-listing-page", "pluralName": "white-papers-listing-pages", "displayName": "white-papers listing page"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"hero_section": {"type": "component", "repeatable": false, "component": "case-study.hero-section"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}}}