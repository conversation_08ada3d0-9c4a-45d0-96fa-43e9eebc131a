{"name": "publish-to-staging", "version": "0.0.0", "description": "The Publish-to-Staging plugin integrates a staging build trigger button into the content manager -> blog details page of Strapi. Users can trigger staging builds by clicking this button to push all the latest changes to staging.", "strapi": {"name": "publish-to-staging", "description": "Description of Publish To Staging plugin", "kind": "plugin", "displayName": "Publish To Staging"}, "dependencies": {"@strapi/design-system": "^1.6.3", "@strapi/helper-plugin": "^4.6.0", "@strapi/icons": "^1.6.3", "prop-types": "^15.7.2"}, "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^5.3.4", "styled-components": "^5.3.6"}, "peerDependencies": {"react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0", "react-router-dom": "^5.2.0", "styled-components": "^5.2.1"}, "author": {"name": "A Strapi developer"}, "maintainers": [{"name": "A Strapi developer"}], "engines": {"node": ">=18.0.0 <=20.x.x", "npm": ">=6.0.0"}, "license": "MIT"}