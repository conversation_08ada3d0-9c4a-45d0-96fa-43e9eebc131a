{"kind": "collectionType", "collectionName": "cloud_consultings", "info": {"singularName": "cloud-consulting", "pluralName": "cloud-consultings", "displayName": "Cloud Consulting", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"cta": {"type": "component", "repeatable": false, "component": "cta.cta"}, "pageName": {"type": "string"}, "slug": {"type": "string", "required": true}, "our_audit_methodology": {"type": "component", "repeatable": false, "component": "audit-methodology.audit-methodology"}, "deliverables": {"type": "component", "repeatable": false, "component": "audit-methodology.audit-methodology"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}, "hero_section": {"type": "component", "repeatable": false, "component": "common.title-desc-image-link"}, "challenges": {"type": "component", "repeatable": false, "component": "audit-methodology.audit-methodology"}, "audit_button": {"type": "component", "repeatable": false, "component": "common.button"}, "solution_with_video": {"type": "component", "repeatable": false, "component": "podcast-page.podcast-series"}, "solution": {"type": "component", "repeatable": false, "component": "common.title-description"}, "why_choose_mtl": {"type": "component", "repeatable": false, "component": "why-choose-mtl.why-choose-mtl"}, "faq": {"type": "component", "repeatable": false, "component": "faq.faq"}, "scope_and_deliverables": {"type": "component", "repeatable": false, "component": "audit-methodology.audit-methodology"}, "tech_stack": {"type": "component", "repeatable": false, "component": "tech-stack.tech-stack"}}}