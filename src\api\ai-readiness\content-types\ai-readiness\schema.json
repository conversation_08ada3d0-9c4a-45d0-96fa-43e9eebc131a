{"kind": "singleType", "collectionName": "ai_readinesses", "info": {"singularName": "ai-readiness", "pluralName": "ai-readinesses", "displayName": "AI Readiness", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"hero_section": {"type": "component", "repeatable": false, "component": "common.title-description-image-button"}, "ai_readiness_components": {"type": "relation", "relation": "oneToMany", "target": "api::ai-readiness-component.ai-readiness-component"}, "form": {"type": "component", "repeatable": false, "component": "form.form"}, "restart_button": {"type": "component", "repeatable": false, "component": "common.button"}, "consultation_button": {"type": "component", "repeatable": false, "component": "common.button"}, "tag_list": {"type": "component", "repeatable": true, "component": "ai-readiness.options"}, "tag": {"type": "component", "repeatable": false, "component": "common.title-description"}, "score_heading": {"type": "string"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}}}