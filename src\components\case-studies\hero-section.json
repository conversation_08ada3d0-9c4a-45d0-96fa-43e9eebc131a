{"collectionName": "components_case_studies_hero_sections", "info": {"displayName": "hero_section", "description": ""}, "options": {}, "attributes": {"tag": {"type": "string"}, "title": {"type": "string"}, "download_button": {"type": "component", "repeatable": false, "component": "common.button"}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "description": {"type": "richtext"}, "global_services": {"type": "relation", "relation": "oneToMany", "target": "api::global-service.global-service"}, "global_industries": {"type": "relation", "relation": "oneToMany", "target": "api::global-industry.global-industry"}}}