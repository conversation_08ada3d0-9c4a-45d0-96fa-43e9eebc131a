{"plugin.name": "Import Export", "plugin.description": "Import/Export data in just few clicks", "plugin.cta.back-to-data-sources": "Back To Data Sources", "plugin.cta.back-to-options": "Back To Options", "plugin.cta.cancel": "Cancel", "plugin.cta.close": "Close", "plugin.cta.copy-to-clipboard": "Copy To Clipboard", "plugin.cta.download-file": "Download File", "plugin.cta.get-data": "Fetch Data", "plugin.cta.export": "Export", "plugin.cta.import": "Import", "plugin.data-format.csv": "CSV [deprecated]", "plugin.data-format.json": "JSON [deprecated]", "plugin.data-format.json-v2": "JSON (v2)", "plugin.form.field.id-field.hint": "Choose the field used as a unique identifier", "plugin.form.field.id-field.label": "Id Field", "plugin.page.homepage.section.quick-actions.title": "Quick Actions", "plugin.page.homepage.section.preferences.title": "Preferences", "plugin.page.homepage.section.need-help.title": "Feature Request / Bug Report", "plugin.page.homepage.section.need-help.description": "Feel free to reach out on the product roadmap, discord or github ✌️", "plugin.page.homepage.section.need-help.discord": "Discord", "plugin.page.homepage.section.need-help.github": "GitHub", "plugin.page.homepage.section.need-help.product-roadmap": "Product Roadmap", "plugin.message.export.error.forbidden.title": "Forbidden", "plugin.message.export.error.forbidden.message": "You don't have permission to read this data type.", "plugin.message.export.error.unexpected.title": "Export failed", "plugin.message.export.error.unexpected.message": "An unexpected error occured while exporting your data.", "plugin.message.import.error.forbidden.title": "Forbidden", "plugin.message.import.error.forbidden.message": "You don't have permission to write this data type.", "plugin.message.import.error.payload-too-large.title": "Payload Too Large", "plugin.message.import.error.payload-too-large.message": "The data size exceeds the file size limit of the server. Checkout the documentation to increase the file size limit.", "plugin.message.import.error.unexpected.title": "Import failed", "plugin.message.import.error.unexpected.message": "An unexpected error occured while importing your data.", "plugin.message.import.success.imported-successfully": "Your data has been imported successfully.", "plugin.message.import.success.imported.title": "Import successful", "plugin.message.import.success.imported.message": "Your data has been imported successfully.", "plugin.message.import.error.imported-partial.title": "Import partially failed", "plugin.message.import.error.imported-partial.message": "Some data failed to be imported. See below for detailed information.", "plugin.import.data-source-step.title": "Select a Data Source", "plugin.import.drag-drop-file": "Drag & drop your file into this area or browse for a file to upload", "plugin.import.file-name": "File name", "plugin.import.importing-data": "Importing data...", "plugin.import.partially-failed": "Import Partially Failed", "plugin.import.detailed-information": "Detailed Information:", "plugin.import.use-code-editor": "Use code editor", "plugin.import.tab.file": "File", "plugin.import.tab.options": "Options", "plugin.export.copied": "<PERSON>pied", "plugin.export.fetching-data": "Fetching data...", "plugin.export.export-format": "Export Format", "plugin.export.options": "Options", "plugin.export.plugins-content-types": "Export plugins content types", "plugin.export.relations-as-id": "Export relations as id.", "plugin.export.apply-filters-and-sort": "Apply filters and sort to exported data.", "plugin.export.deepness": "Deepness", "plugin.export.whole-database": "Whole database"}