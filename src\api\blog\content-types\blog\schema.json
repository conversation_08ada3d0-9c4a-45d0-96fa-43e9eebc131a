{"kind": "collectionType", "collectionName": "blogs", "info": {"singularName": "blog", "pluralName": "blogs", "displayName": "blogs", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true}, "description": {"type": "text", "required": true}, "type": {"type": "enumeration", "enum": ["Agile", "Artificial Intelligence and Machine Learning", "Block Chain", "Bot Development", "Business Strategy", "<PERSON><PERSON><PERSON>", "Cloud", "Data Analytics and Business Intelligence", "Devops", "Low Code No Code Development", "Product Development", "QA", "Robotic Process Automation", "Salesforce Development", "Software Development Practices", "Achievements", "User Experience"], "required": true}, "content": {"displayName": "content", "type": "component", "repeatable": true, "component": "blog.content"}, "suggestions": {"displayName": "suggestions", "type": "component", "repeatable": false, "component": "blog.suggestions"}, "caseStudy_suggestions": {"displayName": "caseStudy_suggestions", "type": "component", "repeatable": false, "component": "blog.case-study-suggestions"}, "slug": {"type": "string", "required": true, "regex": "^[a-zA-Z0-9-]+$"}, "image": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images", "files", "videos", "audios"]}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}, "heroSection_image": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images", "files", "videos", "audios"]}, "authors": {"type": "relation", "relation": "manyToMany", "target": "api::author.author", "mappedBy": "blogs"}, "global_services": {"type": "relation", "relation": "oneToMany", "target": "api::global-service.global-service"}, "global_industries": {"type": "relation", "relation": "oneToMany", "target": "api::global-industry.global-industry"}, "blog_related_service": {"displayName": "blog_related_service", "type": "component", "repeatable": false, "component": "blog.blog-related-service"}, "audio_file": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["audios"]}}}