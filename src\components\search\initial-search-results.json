{"collectionName": "components_search_initial_search_results", "info": {"displayName": "initial_search_results", "description": ""}, "options": {}, "attributes": {"blogs": {"type": "relation", "relation": "oneToMany", "target": "api::blog.blog"}, "case_studies": {"type": "relation", "relation": "oneToMany", "target": "api::case-study.case-study"}, "l2_service_pages": {"type": "relation", "relation": "oneToMany", "target": "api::l2-service-page.l2-service-page"}, "l3_service_pages": {"type": "relation", "relation": "oneToMany", "target": "api::l3-service-page.l3-service-page"}, "industries": {"type": "relation", "relation": "oneToMany", "target": "api::industry.industry"}, "partners": {"type": "relation", "relation": "oneToMany", "target": "api::partner.partner"}, "custom_data_for_initial_search_results": {"displayName": "custom_data_for_initial_search_results", "type": "component", "repeatable": true, "component": "search.custom-data-for-initial-search-results", "max": 5}, "Instructions_doNotChange": {"type": "text", "private": true}}}