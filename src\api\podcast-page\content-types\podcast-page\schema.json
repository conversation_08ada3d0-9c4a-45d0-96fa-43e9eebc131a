{"kind": "singleType", "collectionName": "podcast_pages", "info": {"singularName": "podcast-page", "pluralName": "podcast-pages", "displayName": "Podcast Page", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"hero_section": {"displayName": "hero_section", "type": "component", "repeatable": false, "component": "podcast-page.hero-section"}, "listen_on": {"displayName": "listen_on", "type": "component", "repeatable": false, "component": "podcast-page.listen-on"}, "podcast_series": {"displayName": "podcast_series", "type": "component", "repeatable": true, "component": "podcast-page.podcast-series"}, "latest_episode": {"displayName": "latest_episode", "type": "component", "repeatable": false, "component": "podcast-page.latest-episode"}, "play_button_across_page": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "CTA": {"type": "component", "repeatable": false, "component": "cta.cta"}, "seo": {"type": "component", "repeatable": false, "component": "seo.seo"}}}